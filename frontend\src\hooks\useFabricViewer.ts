/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useRef, useState } from "react";
import { Canvas } from "fabric";
import { updateMeasurementOnModify, isMeasurementLine } from "@/lib/fabric/utils";
import { createHandleSave, createHandleShowOriginal } from "@/lib/fabric/handlers";
import {
  CropData,
  ImageViewerProps,
  FabricObjectState,
  TransformState,
  FabricMeasurementLine,
  FilterParams,
  UndoAction,
} from "@/shared/types";
import { createImageCanvas } from "@/lib/fabric/core";
import { useUndoTracking } from "./useUndoTracking";
import { useFilterManagement } from "./useFilterManagement";
import { useImageTransforms } from "./useImageTransforms";
import { useCropManagement } from "./useCropManagement";

export const useFabricViewer = ({
  data,
  containerRef,
}: ImageViewerProps & {
  containerRef?: React.RefObject<HTMLElement | null>;
}) => {
  // Core canvas and tracking references
  const fabricCanvas = useRef<Canvas | null>(null);
  const eventDisposers = useRef<(() => void)[]>([]);
  const initialObjectCount = useRef(0);
  const originalImageUrl = useRef("");
  const objectStates = useRef(new Map<string, FabricObjectState>());
  const [isShowingOriginal, setIsShowingOriginal] = useState(false);

  // Initialize hooks
  const undoTracking = useUndoTracking(fabricCanvas, initialObjectCount);

  const filterManagement = useFilterManagement(
    {
      brightness: data.viewer.fabricConfigs.brightness,
      contrast: data.viewer.fabricConfigs.contrast,
      grayscale: data.viewer.fabricConfigs.grayscale,
      invert: data.viewer.fabricConfigs.invert,
      sharpness: data.viewer.fabricConfigs.sharpness,
      gammaR: data.viewer.fabricConfigs.gammaR,
      gammaG: data.viewer.fabricConfigs.gammaG,
      gammaB: data.viewer.fabricConfigs.gammaB,
    },
    fabricCanvas
  );

  const imageTransforms = useImageTransforms(
    fabricCanvas,
    data.viewer.fabricConfigs.transformState || {
      rotations: 0,
      flipHorizontal: false,
      flipVertical: false,
    }
  );

  const cropManagement = useCropManagement(
    fabricCanvas,
    data.viewer.fabricConfigs.cropData || {
      normalizedCropRect: undefined,
      canvasDimensions: undefined,
    },
    undoTracking,
    containerRef,
    originalImageUrl
  );
  const isInitializingRef = useRef(false);
  const cachedState = useRef<{
    annotations: any;
    filters: any;
    cropData: CropData;
    transformState: TransformState;
  } | null>(null);

  const setupCanvas = useCallback(
    async (canvasElement: HTMLCanvasElement, imageSource: string) => {
      if (isInitializingRef.current) return;
      isInitializingRef.current = true;

      const result = await createImageCanvas({
        canvasElement,
        imageUrl: imageSource,
        annotations: data.viewer.fabricConfigs.annotations,
        filters: filterManagement.filters as FilterParams,
        cropData: data.viewer.fabricConfigs.cropData as CropData,
        existingCanvas: fabricCanvas.current,
      });

      fabricCanvas.current = result.canvas;
      originalImageUrl.current = imageSource;

      // Crop data is now properly initialized in useCropManagement
      // Only update if there's a significant change
      if (data.viewer.fabricConfigs.cropData) {
        const loadedCropData = data.viewer.fabricConfigs.cropData as CropData;
        const loadedIsCropped = !!loadedCropData.normalizedCropRect;
        const currentIsCropped = !!cropManagement.cropData.normalizedCropRect;
        if (loadedIsCropped !== currentIsCropped) {
          cropManagement.setCropData(loadedCropData);
          cropManagement.setHasPerformedCrop(loadedIsCropped);
        }
      }
      const canvas = fabricCanvas.current;
      initialObjectCount.current = canvas.getObjects().length;

      // Event handler for tracking object additions to undo stack
      const trackAddHandler = (e?: any) => {
        if (!undoTracking.isUndoingRef.current) {
          const addedObject = e?.path || e?.target;
          if (addedObject) {
            const objName = (addedObject as any).name;
            if (objName === "measurement-line") {
              undoTracking.addUndoAction({
                type: "add-measurement",
                lineId: addedObject?.id,
              } as UndoAction);
            } else if (!objName || (!objName.includes("background") && objName !== "crop-rect")) {
              undoTracking.addUndoAction({
                type: "add",
                objectCount: canvas.getObjects().length,
              } as UndoAction);
            }
          }
        }
      };

      // Event handler for tracking object removals to undo stack
      const trackRemoveHandler = () => {
        if (!undoTracking.isUndoingRef.current) {
          undoTracking.addUndoAction({
            type: "remove",
            objectCount: canvas.getObjects().length,
          });
        }
      };

      // Event handler for tracking object modifications to undo stack
      const trackModifyHandler = (e: { target: unknown }) => {
        if (
          undoTracking.isUndoingRef.current ||
          !e.target ||
          (e.target as any)?.name === "measurement-text"
        )
          return;
        const obj = e.target as any;
        const objId = obj.id as string;
        const previousState = objectStates.current.get(objId);
        if (previousState) {
          undoTracking.addUndoAction({ type: "modify", objectId: objId, previousState });
          objectStates.current.delete(objId);
        }
      };

      // Event handler for capturing object state before modification starts
      const startMeasurementHandler = (e: { target: unknown }) => {
        if (
          undoTracking.isUndoingRef.current ||
          !e.target ||
          isMeasurementLine(e.target) ||
          (e.target as any)?.name === "measurement-text"
        )
          return;
        const obj = e.target as any;
        let objId = obj.id as string;
        if (!objId) {
          objId = `obj_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
          obj.id = objId;
        }
        if (!objectStates.current.has(objId)) {
          objectStates.current.set(objId, {
            left: obj.left || 0,
            top: obj.top || 0,
            scaleX: obj.scaleX || 1,
            scaleY: obj.scaleY || 1,
            angle: obj.angle || 0,
          });
        }
      };

      // Event handler for measurement lines with modification tracking
      const modifyMeasurementHandler = (e: { target: unknown }) => {
        if (isMeasurementLine(e.target))
          updateMeasurementOnModify(canvas, e.target as FabricMeasurementLine);
        trackModifyHandler(e);
      };

      // Event handler for measurement line movement updates
      const moveMeasurementHandler = (e: { target: unknown }) => {
        if (isMeasurementLine(e.target))
          updateMeasurementOnModify(canvas, e.target as FabricMeasurementLine);
      };

      // Clean up previous event listeners and attach new ones
      eventDisposers.current.forEach((dispose) => dispose());
      eventDisposers.current = [
        canvas.on("path:created", trackAddHandler),
        canvas.on("object:added", (e) => {
          if (e.target && e.target.type !== "path") {
            trackAddHandler(e);
          }
        }),
        canvas.on("object:removed", trackRemoveHandler),
        canvas.on("object:moving", (e) => {
          startMeasurementHandler(e);
          moveMeasurementHandler(e);
        }),
        canvas.on("object:scaling", (e) => {
          startMeasurementHandler(e);
          moveMeasurementHandler(e);
        }),
        canvas.on("object:rotating", (e) => {
          startMeasurementHandler(e);
          moveMeasurementHandler(e);
        }),
        canvas.on("object:modified", modifyMeasurementHandler),
      ];

      isInitializingRef.current = false;
    },
    [
      data.viewer.fabricConfigs.annotations,
      data.viewer.fabricConfigs.cropData,
      filterManagement.filters,
      cropManagement,
      undoTracking,
    ]
  );

  const handleSave = createHandleSave(
    fabricCanvas,
    data.id,
    filterManagement.filters,
    cropManagement.cropData,
    imageTransforms.transformState
  );

  const handleShowOriginal = createHandleShowOriginal(
    fabricCanvas,
    isShowingOriginal,
    setIsShowingOriginal,
    undoTracking.isUndoingRef,
    cachedState,
    filterManagement.filters,
    cropManagement.cropData,
    imageTransforms.transformState,
    originalImageUrl,
    cropManagement.setCropData,
    imageTransforms.setTransformState,
    cropManagement.setHasPerformedCrop,
    containerRef
  );

  return {
    canvas: fabricCanvas,
    setupCanvas,
    filters: filterManagement.filters,
    updateFilter: filterManagement.updateFilter,
    ...filterManagement.filterHandlers,
    brightness: filterManagement.filters.brightness,
    contrast: filterManagement.filters.contrast,
    grayscale: filterManagement.filters.grayscale,
    invert: filterManagement.filters.invert,
    sharpness: filterManagement.filters.sharpness,
    gammaR: filterManagement.filters.gammaR,
    gammaG: filterManagement.filters.gammaG,
    gammaB: filterManagement.filters.gammaB,
    handleBrightnessChange: filterManagement.filterHandlers.setBrightness,
    handleContrastChange: filterManagement.filterHandlers.setContrast,
    handleGrayscaleChange: filterManagement.filterHandlers.setGrayscale,
    handleInvertChange: filterManagement.filterHandlers.setInvert,
    handleSharpnessChange: filterManagement.filterHandlers.setSharpness,
    handleGammaRChange: filterManagement.filterHandlers.setGammaR,
    handleGammaGChange: filterManagement.filterHandlers.setGammaG,
    handleGammaBChange: filterManagement.filterHandlers.setGammaB,
    handleRotate: imageTransforms.handleRotate,
    handleFlipHorizontal: imageTransforms.handleFlipHorizontal,
    handleFlipVertical: imageTransforms.handleFlipVertical,
    handleUndo: undoTracking.handleUndo,
    handleCrop: cropManagement.handleCrop,
    handleSave,
    handleShowOriginal,
    applySavedTransforms: imageTransforms.applySavedTransforms,
    disableUndoTracking: undoTracking.disableUndoTracking,
    enableUndoTracking: undoTracking.enableUndoTracking,
    canUndo: undoTracking.canUndo,
    undoStack: undoTracking.undoStack,
    isShowingOriginal,
    cropData: cropManagement.cropData,
    setCropData: cropManagement.setCropData,
    hasPerformedCrop: cropManagement.hasPerformedCrop,
  };
};
